{"name": "admin-partner", "version": "0.1.0", "private": true, "main": "main.js", "scripts": {"dev": "cross-env NEXT_WEBPACK_USE_TURBOPACK=1 env-cmd -f .env.development next dev -p 8010", "build:development": "cp .env-development .env.local && next build", "build:staging": "cp .env-staging .env.local && next build", "build:production": "cp .env-production .env.local && next build", "start:dev": "env-cmd -f .env.development next dev", "start:staging": "env-cmd -f .env.staging next dev", "start:prod": "env-cmd -f .env.production next dev", "serve": "npx serve build -p 8011", "lint": "next lint", "lint:fix": "next lint --fix", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "clear-all": "rm -rf node_modules .next out dist build", "re-start": "rm -rf node_modules .next out dist build && yarn install && yarn dev", "re-build": "rm -rf node_modules .next out dist build && yarn install && yarn build", "re-build-test": "rm -rf node_modules .next out dist build && yarn install && yarn run build", "start": "react-scripts start", "electron": "electron .", "build": "npx electron-packager . admin-partner --platform=win32 --arch=x64 --out=build --overwrite"}, "dependencies": {"@ant-design/nextjs-registry": "^1.1.0", "@ckeditor/ckeditor5-build-classic": "41.0.0", "@ckeditor/ckeditor5-react": "6.2.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@firebase/messaging": "^0.12.17", "@hookform/resolvers": "^3.9.1", "@hugerte/hugerte-react": "^1.0.1", "@mui/icons-material": "^6.4.7", "@mui/lab": "^6.0.0-beta.30", "@mui/material": "^6.4.7", "@mui/system": "^6.1.9", "@mui/x-charts": "^7.26.0", "@mui/x-date-pickers": "^7.27.3", "@mui/x-date-pickers-pro": "^7.27.3", "@plick/electron-pos-printer": "^1.3.0", "@reduxjs/toolkit": "^2.4.0", "@types/draft-js": "^0.11.18", "@untitled-ui/icons-react": "^0.1.3", "antd": "^5.26.7", "chart.js": "^4.4.8", "ckeditor5": "^44.3.0", "contenido": "^1.3.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "dotenv": "^16.4.7", "draft-js": "^0.11.7", "electron": "^34.2.0", "emoji-picker-react": "^4.12.2", "escpos": "^3.0.0-alpha.6", "escpos-serialport": "^3.0.0-alpha.4", "escpos-usb": "^3.0.0-alpha.4", "firebase": "^11.6.0", "formik": "^2.4.6", "html-to-image": "^1.11.13", "hugerte": "^1.0.9", "i18next": "^24.0.5", "jodit-react": "^4.1.2", "json-edit-react": "^1.22.2", "lodash.isequal": "^4.5.0", "lucide-react": "^0.535.0", "mapbox-gl": "^3.8.0", "md5": "^2.3.0", "next": "15.0.3", "next-pwa": "^5.6.0", "nextjs-api-lib": "^1.0.7", "notistack": "^3.0.1", "nprogress": "^0.2.0", "numeral": "^2.0.6", "qrcode.react": "^4.2.0", "quill-image-resize-module-react": "^3.0.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-date-range": "^2.0.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.1", "react-hot-toast": "^2.4.1", "react-i18next": "^15.1.3", "react-number-format": "5.4.2", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-scripts": "^5.0.1", "react-virtualized-auto-sizer": "^1.0.25", "react-window": "^1.8.11", "recharts": "^2.15.1", "simplebar-react": "^3.2.6", "stylis-plugin-rtl": "^2.1.1", "usb": "^2.14.0", "uuid": "^11.1.0", "yup": "^1.5.0", "zmp-qrcode": "^3.0.0"}, "devDependencies": {"@types/lodash.isequal": "^4.5.8", "@types/node": "22.10.1", "@types/nprogress": "^0.2.3", "cross-env": "^7.0.3", "electron-packager": "^17.1.2", "env-cmd": "^10.1.0", "eslint": "^8", "eslint-config-next": "15.0.3", "eslint-define-config": "^2.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "5.7.2"}, "prettier": {"trailingComma": "es5", "tabWidth": 2, "singleQuote": false, "semi": true, "printWidth": 100}}